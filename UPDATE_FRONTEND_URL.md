# 🔗 Update Frontend URL ke https://lansia.up.railway.app

## 📋 Overview

Panduan untuk mengupdate semua referensi frontend URL di server untuk menggunakan `https://lansia.up.railway.app/` sebagai frontend URL production.

## 🔧 Files yang Sudah Diupdate

### 1. **server/server.js**
```javascript
// CORS configuration - UPDATED
app.use(cors({
    origin: process.env.FRONTEND_URL || 'https://lansia.up.railway.app',
    credentials: true
}));

// Server startup logging - UPDATED
console.log(`📱 Frontend URL: ${process.env.FRONTEND_URL || 'https://lansia.up.railway.app'}`);
```

### 2. **server/.env.railway.template**
```env
# Frontend URL - UPDATED
FRONTEND_URL=https://lansia.up.railway.app
```

### 3. **RAILWAY_ENV_SETUP.md**
Environment variables template updated dengan frontend URL yang benar.

## 🚀 Railway Environment Variables Setup

### Backend Service Variables (Updated):

```env
DB_HOST=mysql.railway.internal
DB_USER=root
DB_PASSWORD=bpOWXluZucZrMNPNSDvXvMrvwtAWmqKZ
DB_NAME=railway
DB_PORT=3306
PORT=3001
NODE_ENV=production
FRONTEND_URL=https://lansia.up.railway.app
```

### Alternative dengan Railway References:

```env
DB_HOST=${{mysql-database.MYSQL_HOST}}
DB_USER=${{mysql-database.MYSQL_USER}}
DB_PASSWORD=${{mysql-database.MYSQL_PASSWORD}}
DB_NAME=${{mysql-database.MYSQL_DATABASE}}
DB_PORT=${{mysql-database.MYSQL_PORT}}
PORT=3001
NODE_ENV=production
FRONTEND_URL=https://lansia.up.railway.app
```

## 🔄 Deployment Steps

### Step 1: Update Backend Environment Variables

1. **Buka Railway Dashboard**
2. **Go to Backend Service**
3. **Click "Variables" tab**
4. **Add/Update variable:**
   ```
   FRONTEND_URL=https://lansia.up.railway.app
   ```

### Step 2: Redeploy Backend Service

1. **Save environment variables**
2. **Go to "Deployments" tab**
3. **Click "Redeploy"**
4. **Monitor logs** untuk memastikan CORS configuration updated

### Step 3: Verify CORS Configuration

Expected logs setelah redeploy:
```
📱 Frontend URL: https://lansia.up.railway.app
🚀 Server running on port 3001
```

## ✅ Benefits dari Update Ini

### 1. **CORS Configuration**
- ✅ Frontend `https://lansia.up.railway.app` dapat access backend API
- ✅ Tidak ada CORS errors saat production
- ✅ Secure cross-origin requests

### 2. **Fallback Values**
- ✅ Jika `FRONTEND_URL` environment variable tidak set, akan fallback ke production URL
- ✅ Tidak lagi fallback ke `localhost:3000` yang tidak berguna di production

### 3. **Production Ready**
- ✅ Server configuration siap untuk production deployment
- ✅ Logging menunjukkan URL yang benar
- ✅ CORS policy sesuai dengan frontend production URL

## 🔍 Verification

### Test CORS dari Frontend:

1. **Deploy frontend ke `https://lansia.up.railway.app`**
2. **Test API calls dari frontend**
3. **Check browser console** - tidak ada CORS errors
4. **Check backend logs** - requests dari frontend URL diterima

### Test Backend Health:

```bash
curl https://your-backend-url.up.railway.app/health
```

Expected response:
```json
{
  "status": "OK",
  "timestamp": "2024-01-XX...",
  "uptime": 123.45
}
```

## 🎯 Next Steps

1. ✅ Backend server updated dengan frontend URL yang benar
2. ⏳ Deploy backend service dengan environment variables baru
3. ⏳ Deploy frontend service ke `https://lansia.up.railway.app`
4. ⏳ Test cross-origin requests antara frontend dan backend
5. ⏳ Verify semua functionality working di production

## ⚠️ Important Notes

- **URL harus exact match**: `https://lansia.up.railway.app` (tanpa trailing slash)
- **CORS case-sensitive**: Pastikan URL frontend exact sama dengan yang di CORS config
- **Environment variables**: Set `FRONTEND_URL` di Railway backend service variables
- **Redeploy required**: Setiap perubahan environment variables butuh redeploy

Setelah update ini, backend server siap untuk menerima requests dari frontend production di `https://lansia.up.railway.app`!
