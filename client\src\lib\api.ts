import axios from 'axios';

// Base URL untuk API
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://lansia.up.railway.app/';

// Konfigurasi axios
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interceptor untuk request
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Interceptor untuk response
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// Types untuk data
export interface Profile {
  id: number;
  email?: string;
  nama: string;
  usia: number;
  alamat: string;
  riwayat_medis?: string;
  created_at: string;
  updated_at: string;
}

export interface Checkup {
  id: number;
  profile_id: number;
  tekanan_darah?: string;
  gula_darah?: number;
  tanggal: string;
  catatan?: string;
}

export interface ProfileWithCheckups {
  profile: Profile;
  checkups: Checkup[];
}

export interface CreateProfileData {
  email?: string;
  nama: string;
  usia: number;
  alamat: string;
  riwayat_medis?: string;
  tekanan_darah: string;
  gula_darah: number;
  catatan?: string;
}

export interface CreateCheckupData {
  profile_id: number;
  tekanan_darah?: string;
  gula_darah?: number;
  catatan?: string;
}

// API Functions
export const profileAPI = {
  // Mengambil data profil dan riwayat pemeriksaan
  getProfile: async (id: number): Promise<ProfileWithCheckups> => {
    const response = await api.get(`/api/profiles/${id}`);
    return response.data.data;
  },

  // Membuat profil baru
  createProfile: async (data: CreateProfileData): Promise<{ id: number; message: string }> => {
    const response = await api.post('/api/profiles', data);
    return response.data.data;
  },

  // Mengambil semua profil
  getAllProfiles: async (): Promise<Profile[]> => {
    const response = await api.get('/api/profiles');
    return response.data.data;
  },

  // Update profil
  updateProfile: async (id: number, data: Partial<CreateProfileData>): Promise<void> => {
    await api.put(`/api/profiles/${id}`, data);
  },
};

export const checkupAPI = {
  // Menambah pemeriksaan baru
  createCheckup: async (data: CreateCheckupData): Promise<{ id: number; message: string }> => {
    const response = await api.post('/api/checkups', data);
    return response.data.data;
  },

  // Mengambil riwayat pemeriksaan berdasarkan profile_id
  getCheckupsByProfile: async (profileId: number): Promise<Checkup[]> => {
    const response = await api.get(`/api/checkups/profile/${profileId}`);
    return response.data.data;
  },

  // Mengambil detail pemeriksaan
  getCheckup: async (id: number): Promise<Checkup> => {
    const response = await api.get(`/api/checkups/${id}`);
    return response.data.data;
  },

  // Update pemeriksaan
  updateCheckup: async (id: number, data: Partial<CreateCheckupData>): Promise<void> => {
    await api.put(`/api/checkups/${id}`, data);
  },

  // Hapus pemeriksaan
  deleteCheckup: async (id: number): Promise<void> => {
    await api.delete(`/api/checkups/${id}`);
  },
};

// Utility function untuk handle error
export const handleAPIError = (error: unknown): string => {
  if (error && typeof error === 'object' && 'response' in error) {
    const axiosError = error as { response?: { data?: { error?: string } } };
    if (axiosError.response?.data?.error) {
      return axiosError.response.data.error;
    }
  }

  if (error && typeof error === 'object' && 'message' in error) {
    const errorWithMessage = error as { message: string };
    return errorWithMessage.message;
  }

  return 'Terjadi kesalahan yang tidak diketahui';
};

// Health check function
export const healthCheck = async (): Promise<boolean> => {
  try {
    await api.get('/health');
    return true;
  } catch (error) {
    console.error('Health check failed:', error);
    return false;
  }
};

export default api;
