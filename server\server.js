const express = require('express');
const cors = require('cors');
require('dotenv').config();

const { testConnection } = require('./config/database');
const profilesRouter = require('./routes/profiles');
const checkupsRouter = require('./routes/checkups');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
    origin: process.env.FRONTEND_URL || 'https://lansia.up.railway.app',
    credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
    next();
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

// API Routes
app.use('/api/profiles', profilesRouter);
app.use('/api/checkups', checkupsRouter);

// Root endpoint
app.get('/', (req, res) => {
    res.json({
        message: 'Aplikasi Pencatatan Kesehatan Lansia API',
        version: '1.0.0',
        endpoints: {
            profiles: '/api/profiles',
            checkups: '/api/checkups',
            health: '/health'
        }
    });
});

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'Endpoint tidak ditemukan',
        path: req.originalUrl
    });
});

// Global error handler
app.use((error, req, res, next) => {
    console.error('Global error handler:', error);
    
    res.status(error.status || 500).json({
        error: error.message || 'Internal server error',
        ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
    });
});

// Start server
async function startServer() {
    try {
        console.log('🔄 Starting server initialization...');

        // Test database connection
        console.log('🔄 Testing database connection...');
        const dbConnected = await testConnection();
        if (!dbConnected) {
            console.error('❌ Cannot start server: Database connection failed');
            process.exit(1);
        }

        console.log('🔄 Starting HTTP server...');
        app.listen(PORT, () => {
            console.log(`🚀 Server running on port ${PORT}`);
            console.log(`📱 Frontend URL: ${process.env.FRONTEND_URL || 'https://lansia.up.railway.app'}`);
            console.log(`🏥 API Base URL: Railway will provide the actual URL`);
            console.log(`📊 Health Check: /health endpoint available`);
        });

    } catch (error) {
        console.error('❌ Failed to start server:', error);
        console.error('Error stack:', error.stack);
        process.exit(1);
    }
}

// Handle graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('SIGINT received, shutting down gracefully');
    process.exit(0);
});

startServer();
