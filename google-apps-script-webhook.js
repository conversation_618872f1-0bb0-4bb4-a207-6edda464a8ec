/**
 * Google Apps Script untuk Webhook Google Form ke Backend API
 * Aplikasi Pencatatan Kesehatan Lansia - Posyandu Digital
 *
 * DATABASE SCHEMA COMPATIBILITY:
 * - Table: profiles (email, nama, usia, alamat, riwayat_medis)
 * - Table: checkups (profile_id, tekanan_darah, gula_darah, catatan)
 *
 * GOOGLE FORM FIELDS (sesuai https://forms.gle/7oRh9TiSLdvDix7z8):
 * 1. <PERSON><PERSON> (Required, Auto-filled)
 * 2. <PERSON><PERSON> (Required)
 * 3. Us<PERSON> (Required, Number)
 * 4. <PERSON><PERSON><PERSON> (Required, Paragraph)
 * 5. Riwayat Medis (Optional, Paragraph)
 * 6. <PERSON><PERSON><PERSON> (Required, Text format: 120/80)
 * 7. <PERSON><PERSON> (Required, Number in mg/dL)
 *
 * CARA SETUP:
 * 1. Buka Google Apps Script (script.google.com)
 * 2. Buat project baru dan paste kode ini
 * 3. Ganti BACKEND_URL jika perlu (default: http://localhost:3001)
 * 4. Jalankan setupTrigger() untuk menghubungkan dengan form
 * 5. Test dengan runAllTests() untuk memastikan semua berfungsi
 *
 * TESTING FUNCTIONS:
 * - testBackendConnection(): Test koneksi ke server
 * - testFormSubmission(): Simulasi form submission
 * - testMultipleSubmissions(): Test dengan multiple data
 * - runAllTests(): Jalankan semua test sekaligus
 * - setupTrigger(): Setup trigger untuk form
 * - listTriggers(): Lihat trigger yang aktif
 */

// Konfigurasi
const BACKEND_URL = 'http://localhost:3001'; // Ganti dengan URL backend Anda
const API_ENDPOINT = '/api/profiles';

/**
 * Fungsi yang dipanggil ketika form disubmit
 * Setup trigger: Resources > Current project's triggers > Add trigger
 * Choose function: onFormSubmit
 * Select event source: From form
 * Select event type: On form submit
 */
function onFormSubmit(e) {
  try {
    console.log('Form submitted, processing...');
    console.log('Event object:', e);

    // Validasi event object
    if (!e || !e.response) {
      throw new Error('Event object atau response tidak tersedia. Pastikan trigger sudah diset dengan benar.');
    }

    // Ambil response dari form
    const formResponse = e.response;
    const itemResponses = formResponse.getItemResponses();

    console.log('Form response ID:', formResponse.getId());
    console.log('Number of responses:', itemResponses.length);
    
    // Extract data dari form responses
    const formData = {};
    
    itemResponses.forEach(function(itemResponse) {
      const question = itemResponse.getItem().getTitle();
      const answer = itemResponse.getResponse();
      
      // Map pertanyaan ke field database
      switch(question.toLowerCase()) {
        case 'email':
        case 'alamat email':
          formData.email = answer;
          break;
        case 'nama lengkap':
        case 'nama':
          formData.nama = answer;
          break;
        case 'usia':
          formData.usia = parseInt(answer);
          break;
        case 'alamat':
          formData.alamat = answer;
          break;
        case 'riwayat medis':
        case 'riwayat penyakit':
          formData.riwayat_medis = answer;
          break;
        case 'tekanan darah':
          formData.tekanan_darah = answer;
          break;
        case 'gula darah':
        case 'kadar gula darah':
          formData.gula_darah = parseInt(answer);
          break;
        case 'catatan':
        case 'keterangan':
          formData.catatan = answer;
          break;
      }
    });
    
    console.log('Form data extracted:', formData);
    
    // Validasi data wajib sesuai database schema
    console.log('🔍 Validating form data...');

    // Validasi fields yang required di database
    if (!formData.nama || formData.nama.trim() === '') {
      throw new Error('Nama harus diisi (required di database)');
    }

    if (!formData.usia || isNaN(parseInt(formData.usia))) {
      throw new Error('Usia harus diisi dan berupa angka (required di database)');
    }

    if (!formData.alamat || formData.alamat.trim() === '') {
      throw new Error('Alamat harus diisi (required di database)');
    }

    // Validasi fields untuk checkups (diperlukan untuk pemeriksaan)
    if (!formData.tekanan_darah || formData.tekanan_darah.trim() === '') {
      throw new Error('Tekanan darah harus diisi untuk pemeriksaan');
    }

    if (!formData.gula_darah || isNaN(parseInt(formData.gula_darah))) {
      throw new Error('Gula darah harus diisi dan berupa angka untuk pemeriksaan');
    }

    // Validasi format tekanan darah
    const tekananPattern = /^\d{2,3}\/\d{2,3}$/;
    if (!tekananPattern.test(formData.tekanan_darah)) {
      throw new Error('Format tekanan darah tidak valid. Gunakan format: sistol/diastol (contoh: 120/80)');
    }

    console.log('✅ Data validation passed');
    console.log('📊 Validated data:');
    console.log('   - Nama:', formData.nama);
    console.log('   - Usia:', formData.usia, 'tahun');
    console.log('   - Alamat:', formData.alamat.substring(0, 50) + '...');
    console.log('   - Email:', formData.email || 'Not provided');
    console.log('   - Riwayat Medis:', formData.riwayat_medis || 'Tidak ada');
    console.log('   - Tekanan Darah:', formData.tekanan_darah);
    console.log('   - Gula Darah:', formData.gula_darah, 'mg/dL');
    
    // Kirim data ke backend
    const response = sendToBackend(formData);
    
    if (response.success) {
      console.log('Data berhasil dikirim ke backend:', response);
      
      // Optional: Kirim email notifikasi atau update spreadsheet
      sendNotificationEmail(formData, response.data.id);
      
    } else {
      throw new Error('Gagal mengirim data ke backend: ' + response.error);
    }
    
  } catch (error) {
    console.error('Error processing form submission:', error);
    
    // Optional: Kirim email error ke admin
    sendErrorNotification(error.toString(), e);
  }
}

/**
 * Fungsi untuk mengirim data ke backend API
 */
function sendToBackend(formData) {
  try {
    const url = BACKEND_URL + API_ENDPOINT;
    
    const options = {
      'method': 'POST',
      'headers': {
        'Content-Type': 'application/json',
      },
      'payload': JSON.stringify(formData)
    };
    
    console.log('Sending to backend:', url, formData);
    
    const response = UrlFetchApp.fetch(url, options);
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();
    
    console.log('Backend response:', responseCode, responseText);
    
    if (responseCode === 200 || responseCode === 201) {
      return JSON.parse(responseText);
    } else {
      throw new Error(`HTTP ${responseCode}: ${responseText}`);
    }
    
  } catch (error) {
    console.error('Error sending to backend:', error);
    return {
      success: false,
      error: error.toString()
    };
  }
}

/**
 * Fungsi untuk mengirim email notifikasi sukses
 */
function sendNotificationEmail(formData, profileId) {
  try {
    const subject = `✅ Data Lansia Baru Terdaftar: ${formData.nama}`;
    const body = `
🏥 POSYANDU DIGITAL - PENDAFTARAN LANSIA BARU

Data lansia baru telah berhasil didaftarkan dalam sistem:

👤 INFORMASI PROFIL:
• Nama Lengkap: ${formData.nama}
• Email: ${formData.email || 'Tidak tersedia'}
• Usia: ${formData.usia} tahun
• Alamat: ${formData.alamat}
• Riwayat Medis: ${formData.riwayat_medis || 'Tidak ada riwayat khusus'}

🩺 HASIL PEMERIKSAAN:
• Tekanan Darah: ${formData.tekanan_darah}
• Gula Darah: ${formData.gula_darah} mg/dL
• Catatan: ${formData.catatan || 'Tidak ada catatan tambahan'}

📊 INFORMASI SISTEM:
• Profile ID: ${profileId}
• QR Code URL: ${BACKEND_URL.replace('3001', '3000')}/profile/${profileId}
• Waktu Pendaftaran: ${new Date().toLocaleString('id-ID')}

📱 AKSES DATA:
• Scan QR Code untuk melihat profil lengkap
• Akses web: ${BACKEND_URL.replace('3001', '3000')}/profile/${profileId}
• API Endpoint: ${BACKEND_URL}/api/profiles/${profileId}

---
Aplikasi Pencatatan Kesehatan Lansia - Posyandu Digital
    `;

    // Email admin posyandu (ganti dengan email yang sesuai)
    const adminEmail = '<EMAIL>';

    MailApp.sendEmail(adminEmail, subject, body);
    console.log('📧 Notification email sent to:', adminEmail);
    console.log('📋 Email subject:', subject);

  } catch (error) {
    console.error('❌ Error sending notification email:', error);
    console.error('Email error details:', error.toString());
  }
}

/**
 * Fungsi untuk mengirim email error ke admin
 */
function sendErrorNotification(errorMessage, formEvent) {
  try {
    const subject = 'Error: Gagal Memproses Form Lansia';
    const body = `
Terjadi error saat memproses form submission:

Error: ${errorMessage}
Waktu: ${new Date().toLocaleString('id-ID')}
Form ID: ${formEvent ? formEvent.source.getId() : 'Unknown'}

Silakan cek log Google Apps Script untuk detail lebih lanjut.
    `;
    
    // Ganti dengan email admin
    const adminEmail = '<EMAIL>';
    
    MailApp.sendEmail(adminEmail, subject, body);
    console.log('Error notification sent to:', adminEmail);
    
  } catch (error) {
    console.error('Error sending error notification:', error);
  }
}

/**
 * Fungsi untuk testing (manual)
 * Jalankan fungsi ini untuk test koneksi ke backend
 * Data sesuai dengan database schema: profiles + checkups
 */
function testBackendConnection() {
  const testData = {
    // Data untuk tabel profiles
    email: '<EMAIL>',
    nama: 'Test Lansia Webhook',
    usia: 72,
    alamat: 'Jl. Test Webhook No. 999, Jakarta Pusat',
    riwayat_medis: 'Hipertensi ringan, Kolesterol normal',

    // Data untuk tabel checkups
    tekanan_darah: '135/85',
    gula_darah: 125,
    catatan: 'Test submission dari Google Apps Script - Backend Connection Test'
  };

  console.log('🧪 Testing backend connection...');
  console.log('📋 Test data sesuai database schema:');
  console.log('   - Profiles fields: email, nama, usia, alamat, riwayat_medis');
  console.log('   - Checkups fields: tekanan_darah, gula_darah, catatan');
  console.log('📊 Data:', JSON.stringify(testData, null, 2));

  try {
    const result = sendToBackend(testData);

    if (result.success) {
      console.log('✅ Backend connection successful!');
      console.log('📈 Profile ID created:', result.data?.id);
    } else {
      console.log('❌ Backend connection failed:', result.error);
    }

    return result;
  } catch (error) {
    console.error('💥 Test error:', error);
    return { success: false, error: error.toString() };
  }
}

/**
 * Fungsi untuk testing form submission secara manual
 * Simulasi event object dari Google Form sesuai database schema
 */
function testFormSubmission() {
  console.log('🧪 Testing form submission simulation...');
  console.log('📝 Simulating Google Form responses sesuai database schema');

  // Simulasi event object dengan data yang sesuai database schema
  const mockEvent = {
    response: {
      getId: () => 'mock-response-' + Date.now(),
      getItemResponses: () => [
        {
          getItem: () => ({ getTitle: () => 'Email' }),
          getResponse: () => '<EMAIL>'
        },
        {
          getItem: () => ({ getTitle: () => 'Nama Lengkap' }),
          getResponse: () => 'Ibu Simulasi Test'
        },
        {
          getItem: () => ({ getTitle: () => 'Usia' }),
          getResponse: () => '68'
        },
        {
          getItem: () => ({ getTitle: () => 'Alamat' }),
          getResponse: () => 'Jl. Simulasi Form No. 456, Jakarta Timur'
        },
        {
          getItem: () => ({ getTitle: () => 'Riwayat Medis' }),
          getResponse: () => 'Diabetes tipe 2, Hipertensi'
        },
        {
          getItem: () => ({ getTitle: () => 'Tekanan Darah' }),
          getResponse: () => '145/90'
        },
        {
          getItem: () => ({ getTitle: () => 'Gula Darah' }),
          getResponse: () => '165'
        }
      ]
    }
  };

  console.log('📊 Mock form data:');
  console.log('   - Email: <EMAIL>');
  console.log('   - Nama: Ibu Simulasi Test');
  console.log('   - Usia: 68 tahun');
  console.log('   - Alamat: Jl. Simulasi Form No. 456, Jakarta Timur');
  console.log('   - Riwayat Medis: Diabetes tipe 2, Hipertensi');
  console.log('   - Tekanan Darah: 145/90');
  console.log('   - Gula Darah: 165 mg/dL');

  try {
    console.log('🔄 Processing mock form submission...');
    onFormSubmit(mockEvent);
    console.log('✅ Form submission simulation completed successfully');
  } catch (error) {
    console.error('❌ Form submission simulation failed:', error);
    console.error('Error details:', error.toString());
  }
}

/**
 * Fungsi untuk setup trigger otomatis
 * Jalankan sekali untuk setup trigger
 */
function setupTrigger() {
  try {
    console.log('Setting up form trigger...');

    // Hapus trigger lama jika ada
    const triggers = ScriptApp.getProjectTriggers();
    console.log('Existing triggers:', triggers.length);

    triggers.forEach(trigger => {
      if (trigger.getHandlerFunction() === 'onFormSubmit') {
        console.log('Deleting old trigger...');
        ScriptApp.deleteTrigger(trigger);
      }
    });

    // Buat trigger baru
    // ID Form dari URL: https://forms.gle/7oRh9TiSLdvDix7z8
    const formId = '1FAIpQLSdk7Jt4I2Hx15Xt_3aK1TiGjDi1sXM69yBzRS1wXuNewM6Ebw';

    console.log('Opening form with ID:', formId);
    const form = FormApp.openById(formId);
    console.log('Form title:', form.getTitle());

    console.log('Creating new trigger...');
    const trigger = ScriptApp.newTrigger('onFormSubmit')
      .onFormSubmit()
      .create();

    console.log('✅ Trigger berhasil dibuat!');
    console.log('Trigger ID:', trigger.getUniqueId());
    console.log('Form ID:', formId);
    console.log('Form URL: https://docs.google.com/forms/d/e/' + formId + '/viewform');

    return {
      success: true,
      triggerId: trigger.getUniqueId(),
      formId: formId
    };

  } catch (error) {
    console.error('❌ Error setting up trigger:', error);
    return {
      success: false,
      error: error.toString()
    };
  }
}

/**
 * Fungsi untuk melihat semua triggers yang ada
 */
function listTriggers() {
  const triggers = ScriptApp.getProjectTriggers();
  console.log('📋 Total triggers:', triggers.length);

  if (triggers.length === 0) {
    console.log('⚠️ Tidak ada trigger yang ditemukan. Jalankan setupTrigger() terlebih dahulu.');
    return [];
  }

  triggers.forEach((trigger, index) => {
    console.log(`🔧 Trigger ${index + 1}:`);
    console.log('   - Function:', trigger.getHandlerFunction());
    console.log('   - Event Type:', trigger.getEventType());
    console.log('   - Trigger ID:', trigger.getUniqueId());
    console.log('   ---');
  });

  return triggers.map(trigger => ({
    function: trigger.getHandlerFunction(),
    eventType: trigger.getEventType(),
    id: trigger.getUniqueId()
  }));
}

/**
 * Fungsi untuk testing multiple data sesuai database schema
 * Test dengan berbagai variasi data
 */
function testMultipleSubmissions() {
  console.log('🧪 Testing multiple form submissions...');

  const testCases = [
    {
      email: '<EMAIL>',
      nama: 'Pak Ahmad Suharto',
      usia: 75,
      alamat: 'Jl. Merdeka No. 12, Jakarta Pusat',
      riwayat_medis: 'Hipertensi, Kolesterol tinggi',
      tekanan_darah: '150/95',
      gula_darah: 180,
      catatan: 'Pemeriksaan rutin bulanan - Test Case 1'
    },
    {
      email: '<EMAIL>',
      nama: 'Ibu Siti Nurhaliza',
      usia: 68,
      alamat: 'Jl. Sudirman No. 45, Jakarta Selatan',
      riwayat_medis: 'Diabetes tipe 2',
      tekanan_darah: '140/85',
      gula_darah: 200,
      catatan: 'Kontrol gula darah - Test Case 2'
    },
    {
      email: '<EMAIL>',
      nama: 'Pak Budi Santoso',
      usia: 72,
      alamat: 'Jl. Thamrin No. 78, Jakarta Barat',
      riwayat_medis: null, // Test dengan riwayat medis kosong
      tekanan_darah: '120/80',
      gula_darah: 95,
      catatan: 'Kondisi sehat - Test Case 3'
    }
  ];

  console.log(`📊 Testing ${testCases.length} different cases...`);

  testCases.forEach((testData, index) => {
    console.log(`\n🔄 Test Case ${index + 1}: ${testData.nama}`);
    console.log(`   Email: ${testData.email}`);
    console.log(`   Usia: ${testData.usia} tahun`);
    console.log(`   Tekanan Darah: ${testData.tekanan_darah}`);
    console.log(`   Gula Darah: ${testData.gula_darah} mg/dL`);

    try {
      const result = sendToBackend(testData);

      if (result.success) {
        console.log(`   ✅ Success - Profile ID: ${result.data?.id || 'N/A'}`);
      } else {
        console.log(`   ❌ Failed: ${result.error}`);
      }
    } catch (error) {
      console.log(`   💥 Error: ${error.toString()}`);
    }
  });

  console.log('\n🎉 Multiple submissions test completed!');
}

/**
 * Fungsi untuk menjalankan semua test secara berurutan
 * Comprehensive testing suite
 */
function runAllTests() {
  console.log('🚀 Starting comprehensive test suite...');
  console.log('📋 Database Schema: profiles (email, nama, usia, alamat, riwayat_medis) + checkups (tekanan_darah, gula_darah, catatan)');
  console.log('🔗 Backend URL:', BACKEND_URL);
  console.log('');

  try {
    // Test 1: Backend Connection
    console.log('=== TEST 1: Backend Connection ===');
    testBackendConnection();

    console.log('\n=== TEST 2: Form Submission Simulation ===');
    testFormSubmission();

    console.log('\n=== TEST 3: Multiple Data Submissions ===');
    testMultipleSubmissions();

    console.log('\n=== TEST 4: Trigger Status ===');
    listTriggers();

    console.log('\n🎯 All tests completed! Check the logs above for results.');

  } catch (error) {
    console.error('💥 Test suite failed:', error);
  }
}
