# 🚀 Railway Deployment Guide

Panduan lengkap untuk deploy folder `client/` dan `server/` ke Railway sebagai services terpisah.

## 📋 Prerequisites

1. Akun Railway (daftar di [railway.app](https://railway.app))
2. Folder `client/` dan `server/` sudah siap dengan konfigurasi production
3. File `nixpacks.toml` sudah ada di masing-masing folder

## 🗂️ Struktur Deployment

```
Railway Project: posyandu-digital
├── 🗄️ MySQL Database Service
├── 🔧 Backend Service (dari folder server/)
└── 🌐 Frontend Service (dari folder client/)
```

## 🔧 Step-by-Step Deployment

### 1. Create Railway Project

1. Login ke [railway.app](https://railway.app)
2. Click "New Project" → "Empty Project"
3. Project name: `posyandu-digital`

### 2. Add MySQL Database

1. Click "Add Service" → "Database" → "MySQL"
2. Service name: `mysql-database`
3. Wait for deployment to complete
4. Copy connection details dari Variables tab

### 3. Deploy Backend Service (folder server/)

1. Click "Add Service" → "GitHub Repo"
2. Connect GitHub dan pilih repository
3. Configure service:
   - **Service name**: `backend-api`
   - **Root directory**: `server`
   - **Build command**: `npm ci` (auto-detected)
   - **Start command**: `npm start` (auto-detected)

#### Environment Variables untuk Backend:
```env
DB_HOST=<mysql-host-from-railway>
DB_USER=<mysql-user-from-railway>
DB_PASSWORD=<mysql-password-from-railway>
DB_NAME=<mysql-database-from-railway>
DB_PORT=<mysql-port-from-railway>
PORT=3001
NODE_ENV=production
FRONTEND_URL=<frontend-url-will-be-set-later>
```

### 4. Deploy Frontend Service (folder client/)

1. Click "Add Service" → "GitHub Repo"
2. Connect GitHub dan pilih repository yang sama
3. Configure service:
   - **Service name**: `frontend-web`
   - **Root directory**: `client`
   - **Build command**: `npm run build` (auto-detected)
   - **Start command**: `npm start` (auto-detected)

#### Environment Variables untuk Frontend:
```env
NEXT_PUBLIC_API_URL=<backend-url-from-railway>
NEXT_PUBLIC_APP_NAME=Posyandu Digital
NEXT_PUBLIC_APP_VERSION=1.0.0
```

### 5. Update Cross-Service URLs

1. Copy backend service URL dari Railway dashboard
2. Update frontend service `NEXT_PUBLIC_API_URL` dengan backend URL
3. Copy frontend service URL dari Railway dashboard
4. Update backend service `FRONTEND_URL` dengan frontend URL
5. Redeploy both services untuk apply changes

### 6. Import Database Schema

1. Connect ke Railway MySQL menggunakan MySQL client
2. Import file `database/migrate-to-railway.sql`
3. Verify tables dan sample data berhasil dibuat

## 🔍 Verification Steps

1. ✅ MySQL database service running dan accessible
2. ✅ Backend service (server/) deployed dan running
3. ✅ Frontend service (client/) deployed dan running
4. ✅ Backend health endpoint responding: `/health`
5. ✅ Frontend dapat load dan connect ke backend API
6. ✅ Database queries working dari backend
7. ✅ QR code functionality working di frontend

## 🐛 Troubleshooting

### Common Issues:

1. **Root Directory Not Found**
   - Pastikan "Root directory" diset ke `server` atau `client`
   - Check repository structure di GitHub

2. **Database Connection Failed**
   - Check environment variables di backend service
   - Verify MySQL service is running
   - Test connection menggunakan `database/test-connection.js`

3. **CORS Errors**
   - Verify `FRONTEND_URL` di backend service variables
   - Check CORS configuration di `server/server.js`

4. **Build Failures**
   - Check Node.js version compatibility (>=18.0.0)
   - Verify `nixpacks.toml` ada di folder yang benar
   - Check package.json scripts

5. **Environment Variables Not Loading**
   - Verify variable names (case-sensitive)
   - Check for typos di Railway dashboard
   - Ensure variables are set di correct service

## 📞 Support

Jika mengalami masalah:
1. Check Railway logs untuk error details
2. Verify semua environment variables
3. Test API endpoints secara manual
4. Check database connection

## 🎯 Production Checklist

- [ ] All services deployed successfully
- [ ] Environment variables configured
- [ ] Database schema imported
- [ ] Cross-service communication working
- [ ] QR code scanning functional
- [ ] Data persistence working
- [ ] Error handling working
- [ ] Performance acceptable
