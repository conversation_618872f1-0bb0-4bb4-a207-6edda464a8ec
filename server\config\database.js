const mysql = require('mysql2/promise');
require('dotenv').config();

// Railway database configuration
const dbConfig = {
    host: process.env.DB_HOST || process.env.MYSQL_HOST || 'mysql.railway.internal',
    user: process.env.DB_USER || process.env.MYSQL_USER || 'root',
    password: process.env.DB_PASSWORD || process.env.MYSQL_PASSWORD || 'bpOWXluZucZrMNPNSDvXvMrvwtAWmqKZ',
    database: process.env.DB_NAME || process.env.MYSQL_DATABASE || 'railway',
    port: parseInt(process.env.DB_PORT || process.env.MYSQL_PORT || '3306'),
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0,
    // Railway MySQL requires SSL in production
    ssl: process.env.NODE_ENV === 'production' ? {
        rejectUnauthorized: false
    } : false,
    // Use valid connection options for Railway
    connectTimeout: 60000,
    // acquireTimeout is not a valid MySQL2 connection option - removed to fix warning
    // acquireTimeout: 60000
};

// Membuat connection pool
const pool = mysql.createPool(dbConfig);

// Test koneksi database dengan detailed logging
async function testConnection() {
    try {
        console.log('🔍 Database config:', {
            host: dbConfig.host,
            user: dbConfig.user,
            database: dbConfig.database,
            port: dbConfig.port,
            ssl: !!dbConfig.ssl
        });

        const connection = await pool.getConnection();
        console.log('✅ Database connected successfully');

        // Test simple query
        const [rows] = await connection.execute('SELECT 1 as test');
        console.log('✅ Database query test successful:', rows);

        connection.release();
        return true;
    } catch (error) {
        console.error('❌ Database connection failed:', error.message);
        console.error('🔍 Error details:', {
            code: error.code,
            errno: error.errno,
            sqlState: error.sqlState
        });
        return false;
    }
}

// Export pool dan test function
module.exports = {
    pool,
    testConnection
};
