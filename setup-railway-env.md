# 🔧 Railway Environment Variables Setup

## 📋 Langkah-langkah Setup Environment Variables

### 1. Database Service Variables (Otomatis)
Railway MySQL service akan otomatis generate variables berikut:
- `MYSQL_HOST`
- `MYSQL_USER` 
- `MYSQL_PASSWORD`
- `MYSQL_DATABASE`
- `MYSQL_PORT`
- `DATABASE_URL`

### 2. Backend Service Variables

Masuk ke **Railway Dashboard > Backend Service > Variables** dan tambahkan:

```env
# Database (copy dari MySQL service)
DB_HOST=${{MYSQL_HOST}}
DB_USER=${{MYSQL_USER}}
DB_PASSWORD=${{MYSQL_PASSWORD}}
DB_NAME=${{MYSQL_DATABASE}}
DB_PORT=${{MYSQL_PORT}}

# Server Config
PORT=3001
NODE_ENV=production

# Frontend URL (isi setelah frontend deploy)
FRONTEND_URL=https://your-frontend-service.up.railway.app
```

### 3. Frontend Service Variables

Masuk ke **Railway Dashboard > Frontend Service > Variables** dan tambahkan:

```env
# API URL (isi setelah backend deploy)
NEXT_PUBLIC_API_URL=https://your-backend-service.up.railway.app

# App Config
NEXT_PUBLIC_APP_NAME=Posyandu Digital
NEXT_PUBLIC_APP_VERSION=1.0.0
```

## 🔄 Update URLs Setelah Deployment

### Step 1: Deploy Backend
1. Deploy backend service dulu
2. Copy backend service URL dari Railway dashboard
3. Format: `https://backend-service-name-production.up.railway.app`

### Step 2: Update Frontend Variables
1. Masuk ke Frontend Service > Variables
2. Update `NEXT_PUBLIC_API_URL` dengan backend URL
3. Redeploy frontend service

### Step 3: Update Backend Variables  
1. Deploy frontend service
2. Copy frontend service URL dari Railway dashboard
3. Masuk ke Backend Service > Variables
4. Update `FRONTEND_URL` dengan frontend URL
5. Redeploy backend service

## 🎯 Variable Reference Template

### Backend Service Environment Variables:
```
DB_HOST=containers-us-west-1.railway.app
DB_USER=root
DB_PASSWORD=abc123xyz789
DB_NAME=railway
DB_PORT=6543
PORT=3001
NODE_ENV=production
FRONTEND_URL=https://posyandu-frontend-production.up.railway.app
```

### Frontend Service Environment Variables:
```
NEXT_PUBLIC_API_URL=https://posyandu-backend-production.up.railway.app
NEXT_PUBLIC_APP_NAME=Posyandu Digital
NEXT_PUBLIC_APP_VERSION=1.0.0
```

## ⚠️ Important Notes

1. **Variable Names**: Harus exact match dengan yang digunakan di code
2. **NEXT_PUBLIC_**: Prefix wajib untuk client-side variables di Next.js
3. **Railway Variables**: Gunakan `${{VARIABLE_NAME}}` untuk reference Railway variables
4. **URLs**: Pastikan tidak ada trailing slash di URLs
5. **Redeploy**: Setiap perubahan environment variables butuh redeploy service

## 🔍 Verification

Test environment variables dengan:

### Backend Health Check:
```bash
curl https://your-backend-url.up.railway.app/health
```

### Frontend Environment:
Check browser console untuk `process.env` values

### Database Connection:
Check backend logs untuk database connection status
