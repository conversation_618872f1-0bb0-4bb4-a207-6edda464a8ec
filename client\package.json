{"name": "posyandu-digital-client", "version": "1.0.0", "private": true, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "echo 'Client dependencies installed successfully'", "export": "next export"}, "dependencies": {"axios": "^1.11.0", "html5-qrcode": "^2.3.8", "next": "15.4.3", "qrcode.react": "^4.2.0", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/qrcode.react": "^1.0.5", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "9.31.0", "eslint-config-next": "15.4.3", "tailwindcss": "^4", "typescript": "^5"}, "keywords": ["nextjs", "react", "posyandu", "qr-code", "healthcare", "railway"], "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/lansia.git"}, "homepage": "https://github.com/your-username/lansia#readme"}