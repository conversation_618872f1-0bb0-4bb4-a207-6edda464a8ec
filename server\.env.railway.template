# Railway Production Environment Variables Template
# Copy these variables to Railway Dashboard > Backend Service > Variables

# Database Configuration (akan diisi otomatis dari Railway MySQL service)
DB_HOST=mysql.railway.internal
DB_USER=root
DB_PASSWORD=<MYSQL_PASSWORD_FROM_RAILWAY>
DB_NAME=railway
DB_PORT=3306

# Server Configuration
PORT=3001
NODE_ENV=production

# Frontend URL (gunakan URL frontend Railway yang sudah di-deploy)
FRONTEND_URL=https://lansia.up.railway.app

# Example values setelah deployment:
# DB_HOST=containers-us-west-1.railway.app
# DB_USER=root
# DB_PASSWORD=abc123xyz789
# DB_NAME=railway
# DB_PORT=6543
# FRONTEND_URL=https://lansia.up.railway.app
