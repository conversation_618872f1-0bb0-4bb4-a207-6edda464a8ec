# 🔧 Railway Environment Variables Setup

## 📊 MySQL Credentials dari Railway

Berdasarkan MySQL service Anda, berikut adalah credentials yang benar:

```
MYSQL_DATABASE: railway
MYSQL_HOST: mysql.railway.internal
MYSQL_PASSWORD: bpOWXluZucZrMNPNSDvXvMrvwtAWmqKZ
MYSQL_PORT: 3306
MYSQL_USER: root
```

## 🚀 Set Environment Variables di Backend Service

### Step 1: Buka Railway Dashboard
1. Go to **Backend Service** (yang CRASHED)
2. Click tab **"Variables"**

### Step 2: Add Environment Variables

Copy dan paste variables berikut **satu per satu**:

```env
DB_HOST=mysql.railway.internal
DB_USER=root
DB_PASSWORD=bpOWXluZucZrMNPNSDvXvMrvwtAWmqKZ
DB_NAME=railway
DB_PORT=3306
PORT=3001
NODE_ENV=production
FRONTEND_URL=https://lansia.up.railway.app
```

**ATAU** gunakan Railway variable references (recommended):

```env
DB_HOST=${{mysql-database.MYSQL_HOST}}
DB_USER=${{mysql-database.MYSQL_USER}}
DB_PASSWORD=${{mysql-database.MYSQL_PASSWORD}}
DB_NAME=${{mysql-database.MYSQL_DATABASE}}
DB_PORT=${{mysql-database.MYSQL_PORT}}
PORT=3001
NODE_ENV=production
FRONTEND_URL=https://lansia.up.railway.app
```

### Step 3: Redeploy Backend Service

1. **Save** semua environment variables
2. Go to **"Deployments"** tab
3. Click **"Redeploy"**
4. **Monitor logs** untuk memastikan connection berhasil

## ✅ Expected Success Logs

Setelah redeploy, logs harus menunjukkan:

```
🔍 Database config: {
  host: 'mysql.railway.internal',
  user: 'root',
  database: 'railway',
  port: 3306,
  ssl: true
}
✅ Database connected successfully
✅ Database query test successful: [ { test: 1 } ]
🚀 Server running on port 3001
📱 Frontend URL: http://localhost:3000
🏥 API Base URL: http://localhost:3001
📊 Health Check: http://localhost:3001/health
```

## 🔍 Troubleshooting

### Issue 1: Still ECONNREFUSED
**Check:**
- Environment variables saved correctly
- No typos in variable names
- Backend service redeployed after setting variables

### Issue 2: Authentication Failed
**Check:**
- Password copied exactly: `bpOWXluZucZrMNPNSDvXvMrvwtAWmqKZ`
- No extra spaces in password
- Username is exactly `root`

### Issue 3: Database Not Found
**Check:**
- Database name is `railway` (not `lansia`)
- MySQL service is running and accessible

## 📋 Verification Steps

1. ✅ Set all environment variables in backend service
2. ✅ Redeploy backend service
3. ✅ Check logs for successful database connection
4. ✅ Backend service status shows "Active" (not CRASHED)
5. ✅ Test health endpoint: `https://your-backend-url.up.railway.app/health`

## 🎯 Next Steps After Backend is Running

1. **Import Database Schema:**
   ```bash
   # Connect to Railway MySQL and run:
   # database/migrate-to-railway.sql
   ```

2. **Deploy Frontend Service:**
   - Set `NEXT_PUBLIC_API_URL` to backend URL
   - Deploy client/ folder

3. **Update Cross-Service URLs:**
   - Update backend `FRONTEND_URL` with frontend URL
   - Redeploy both services

## 📞 Quick Fix Commands (Railway CLI)

If you have Railway CLI:

```bash
# Set environment variables
railway variables set DB_HOST=mysql.railway.internal
railway variables set DB_USER=root
railway variables set DB_PASSWORD=bpOWXluZucZrMNPNSDvXvMrvwtAWmqKZ
railway variables set DB_NAME=railway
railway variables set DB_PORT=3306
railway variables set NODE_ENV=production
railway variables set PORT=3001

# Redeploy
railway redeploy
```

## ⚠️ Important Notes

- **Password is case-sensitive**: `bpOWXluZucZrMNPNSDvXvMrvwtAWmqKZ`
- **Host is internal**: `mysql.railway.internal` (not external URL)
- **Database name**: `railway` (Railway default)
- **SSL**: Automatically enabled in production mode
- **Port**: Standard MySQL port `3306`

Setelah set environment variables dan redeploy, backend service seharusnya tidak CRASHED lagi!
